import {
  StyleSheet,
} from "react-native";

export default StyleSheet.create({
  userSpeakingBubble: {
    position: 'absolute',
    bottom: -20,
    alignSelf: 'center',
    backgroundColor: '#4CAF50',
    padding: 10,
    borderRadius: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  userWavesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 20,
  },
  userWave: {
    width: 3,
    marginHorizontal: 1,
    backgroundColor: '#ffffff',
    borderRadius: 2,
  },
  userWave1: {
    height: 10,
    opacity: 0.4,
  },
  userWave2: {
    height: 16,
    opacity: 0.7,
  },
  userWave3: {
    height: 12,
    opacity: 0.5,
  },
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    paddingTop: 60,
    paddingBottom: 40,
  },
  timer: {
    color: '#4caf50',
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 10,
  },
  avatarContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 30,
  },
  avatarCircle: {
    width: 200,
    height: 200,
    borderRadius: 100,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
    overflow: 'hidden',
  },
  avatar: {
    width: '100%',
    height: '100%',
    borderRadius: 100,
  },
  speakingBubble: {
    position: 'absolute',
    top: 20,
    right: -20,
    backgroundColor: '#4285F4',
    borderRadius: 25,
    padding: 10,
    width: 50,
    height: 50,
    alignItems: 'center',
    justifyContent: 'center',
  },
  wavesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: 30,
  },
  wave: {
    width: 6,
    backgroundColor: 'white',
    borderRadius: 3,
  },
  wave1: {
    height: 10,
    animationName: 'wave',
    animationDuration: '1s',
    animationIterationCount: 'infinite',
  },
  wave2: {
    height: 16,
    animationName: 'wave',
    animationDuration: '1s',
    animationDelay: '0.2s',
    animationIterationCount: 'infinite',
  },
  wave3: {
    height: 10,
    animationName: 'wave',
    animationDuration: '1s',
    animationDelay: '0.4s',
    animationIterationCount: 'infinite',
  },
  textContainer: {
    alignItems: 'center',
    marginVertical: 20,
    paddingHorizontal: 20,
  },
  responseText: {
    color: 'white',
    fontSize: 18,
    textAlign: 'center',
    marginBottom: 10,
  },
  translatedText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 16,
    textAlign: 'center',
    fontFamily: 'Estedad-Regular',
  },
  interruptArea: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  interruptText: {
    color: 'rgba(255, 255, 255, 0.5)',
    fontSize: 14,
  },
  controlsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginBottom: 20,
  },
  controlButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  primaryButton: {
    backgroundColor: '#F44336',
  },
});