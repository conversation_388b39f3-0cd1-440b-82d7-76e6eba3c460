import { useState, useEffect, useCallback } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>EventEmitter,
} from "react-native";
import * as Speech from "expo-speech";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useFocusEffect } from "@react-navigation/native";
import { useAppSelector, useAppDispatch } from "../../store/hooks";
import { setTextOfSpeech } from "../../store/slices/modelSlice";

/**
 * Custom hook for managing chat state and effects
 */
export const useChatState = (
  topicItem?: any,
  navigation?: any,
  comesFrom?: string
) => {
  const [chatId, setChatId] = useState<string>("");
  const [inputValue, setInputValue] = useState("");
  const [isSpeaking, setIsSpeaking] = useState(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [playSpeech, setPlaySpeech] = useState(false);
  const [requested, setRequested] = useState(false);
  const [scores, setScores] = useState({});
  const [textSubmitted, setTextSubmitted] = useState(false);
  const [chatFinished, setChatFinished] = useState(false);
  const [hasReachedDailyLimit, setHasReachedDailyLimit] = useState(false);
  const [isScreenFocused, setIsScreenFocused] = useState(false);
  // Removed showCongratulations state as we're directly showing the assessment screen
  const [approvedNewChat, setApprovedNewChat] = useState(false);
  const dispatch = useAppDispatch();

  const keySize = 10000;

  interface TranscriptionItem {
    key: number;
    type: "req" | "res" | "loading";
    text: string;
    isTopic: boolean;
    isRelevant?: boolean; // Optional properties based on usage
    correctedText?: string;
    _id?: string;
  }

  const [transcriptionResult, setTranscriptionResult] = useState<
    TranscriptionItem[] | []
  >([]);

  const { Session, newChat, textOfSpeech } = useAppSelector(
    (state) => state.model
  );

  // Set up navigation title
  useEffect(() => {
    navigation.setOptions({ title: topicItem.title });
  }, [topicItem._id]);

  // Track screen focus state
  useFocusEffect(
    useCallback(() => {
      setIsScreenFocused(true);
      return () => {
        setIsScreenFocused(false);
      };
    }, [])
  );

  // Handle text of speech updates
  useEffect(() => {
    if (textOfSpeech && isScreenFocused) {
      setInputValue(textOfSpeech);
      dispatch(setTextOfSpeech({ textOfSpeech: "" }));
    }
  }, [textOfSpeech, isScreenFocused]);

  // Handle back button
  useEffect(() => {
    const backAction = async () => {
      Speech.stop();
      setIsSpeaking(null);
      if (topicItem.title === "free talk") {
        navigation.navigate("homeScreen");
        return;
      } else if (comesFrom === "DailyLesson" || comesFrom === "LastLesson" || comesFrom === "VoiceAssistant") {
        navigation.navigate("homeScreen");
        return;
      }

      // Only refresh if chat is finished and has scores
      else if (chatFinished && scores?.average) {
        try {
          // Get completed topics from AsyncStorage
          const completedTopicsStr = await AsyncStorage.getItem(
            "completedTopics"
          );
          let completedTopics = completedTopicsStr
            ? JSON.parse(completedTopicsStr)
            : [];

          // Check if this topic has been completed before
          const isCompletedBefore = completedTopics.includes(topicItem._id);
          const index = completedTopics.indexOf(topicItem._id);
          if (index > -1) {
            completedTopics.splice(index, 1);
          }
          await AsyncStorage.setItem(
            "completedTopics",
            JSON.stringify(completedTopics)
          );

          navigation.navigate("topic", {
            refresh: isCompletedBefore,
            lastTopicId: topicItem._id,
          });
        } catch (error) {
          console.log("Error handling back button:", error);
          navigation.navigate("topic", {
            refresh: false,
            lastTopicId: topicItem._id,
          });
        }
      } else {
        // If chat not finished or no scores, just navigate back without refresh
        navigation.navigate("topic", {
          lastTopicId: topicItem._id,
        });
      }

      return true;
    };

    const backHandler = BackHandler.addEventListener(
      "hardwareBackPress",
      backAction
    );

    return () => backHandler.remove();
  }, [chatFinished, scores]);

  // Store completed topic
  useEffect(() => {
    const storeCompletedTopic = async () => {
      if (chatFinished && scores?.average) {
        try {
          // Get completed topics from AsyncStorage
          const completedTopicsStr = await AsyncStorage.getItem(
            "completedTopics"
          );
          let completedTopics = completedTopicsStr
            ? JSON.parse(completedTopicsStr)
            : [];

          // Check if this topic has been completed before
          if (!completedTopics.includes(topicItem._id)) {
            // Add this topic to completed topics
            completedTopics.push(topicItem._id);
            await AsyncStorage.setItem(
              "completedTopics",
              JSON.stringify(completedTopics)
            );
          }
        } catch (error) {
          console.log("Error storing completed topic:", error);
        }
      }
    };

    storeCompletedTopic();
  }, [chatFinished, scores]);

  // Handle assessment modal or navigation
  const handleOpenAssessmentModal = () => {
    // For all scenarios, navigate to the assessment screen
    navigation.navigate("gamification", {
      chatId: topicItem._id,
      topicItem: {
        _id: topicItem._id,
      },
      transcriptionResult,
    });
  };

  useFocusEffect(
    useCallback(() => {
      DeviceEventEmitter.addListener(
        "openAssessmentModal",
        handleOpenAssessmentModal
      );
      return () => {
        DeviceEventEmitter.removeAllListeners("openAssessmentModal");
      };
    }, [transcriptionResult])
  );

  return {
    chatId,
    setChatId,
    inputValue,
    setInputValue,
    isSpeaking,
    setIsSpeaking,
    isModalVisible,
    setIsModalVisible,
    playSpeech,
    setPlaySpeech,
    requested,
    setRequested,
    scores,
    setScores,
    textSubmitted,
    setTextSubmitted,
    chatFinished,
    setChatFinished,
    hasReachedDailyLimit,
    setHasReachedDailyLimit,
    transcriptionResult,
    setTranscriptionResult,
    Session,
    newChat,
    approvedNewChat,
    setApprovedNewChat,
    keySize,
    // showCongratulations and setShowCongratulations removed
  };
};
