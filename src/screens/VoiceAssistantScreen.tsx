import React from "react";
import { StatusBar } from "expo-status-bar";
import { useIsFocused } from "@react-navigation/native";
import VoiceAssistant from "../components/voiceAssistantUi";
import { View, StyleSheet } from "react-native";
import { useAppDispatch, useAppSelector } from "../store/hooks";
import { initNewChat } from "../store/slices/modelSlice";
import { useChatState } from "../components/chat/chatHooks";
import { useVoiceAssistantHooks } from "./useVoiceAssistantHooks";

export default function VoiceAssistantScreen({ route, navigation }: any) {
  // Free Talk topic item - same as in FreeTalk.tsx
  const topicItem = {
    _id: "67ba493b70ebab475c063a79",
    title: "free talk",
    scenario:
      "You are an AI conversation partner designed to engage users in a natural, free-flowing conversation. Your goal is to start a friendly and open-ended chat that makes the user feel comfortable and eager to respond. Avoid robotic or overly formal phrasing. Use dynamic opening",
  };

  const dispatch = useAppDispatch();
  const { level, isPremium, coins } = useAppSelector((state) => state.auth);
  const {
    chatId,
    setChatId,
    playSpeech,
    setPlaySpeech,
    requested,
    setRequested,
    setScores,
    textSubmitted,
    setTextSubmitted,
    setChatFinished,
    setHasReachedDailyLimit,
    transcriptionResult,
    setTranscriptionResult,
    newChat,
    approvedNewChat,
    keySize,
  } = useChatState(topicItem, navigation, "VoiceAssistant");

  const isFocused = useIsFocused();
  const {
    isRecording,
    isPaused,
    botIsSpeaking,
    currentResponse,
    translatedResponse,
    startRecordingSession,
    handleStopRecording,
    handleEndCall,
    handlePauseResume,
    setBotIsSpeaking,
  } = useVoiceAssistantHooks({
    topicItem,
    navigation,
    level,
    isPremium,
    coins,
    chatState: {
      chatId,
      setChatId,
      playSpeech,
      setPlaySpeech,
      requested,
      setRequested,
      setScores,
      textSubmitted,
      setTextSubmitted,
      setChatFinished,
      setHasReachedDailyLimit,
      transcriptionResult,
      setTranscriptionResult,
      newChat,
      approvedNewChat,
      keySize,
      dispatch,
    },
  });

  return (
    <View style={styles.container}>
      {isFocused && <StatusBar style="light" />}
      <VoiceAssistant
        isSpeaking={botIsSpeaking}
        setIsSpeaking={(value: boolean | null) => setBotIsSpeaking(value === true)}
        onSpeechEnd={undefined}
        handleEndCall={handleEndCall}
        isUserSpeaking={isRecording}
        handlePauseResume={handlePauseResume}
        isPaused={isPaused}
        responseText={currentResponse}
        translatedText={translatedResponse}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
