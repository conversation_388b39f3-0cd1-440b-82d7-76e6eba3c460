import { useEffect, useState } from "react";
import { useIsFocused } from "@react-navigation/native";
import * as Speech from "expo-speech";
import { useAppDispatch, useAppSelector } from "../store/hooks";
import { initNewChat } from "../store/slices/modelSlice";
import { useChatState } from "../components/chat/chatHooks";
import { startTopic, chatRequest } from "../components/chat/chatActions";
import { getTranslation } from "../components/chat/chatUtils";
import { handleRecordingComplete } from "../components/handleTranscription";
import { initRecording, startRecording, stopRecording } from "../utils/recordingUtils";

export function useVoiceAssistantHooks({
  topicItem,
  navigation,
  level,
  isPremium,
  coins,
  chatState
}: any) {
  // Voice-specific state
  const [isRecording, setIsRecording] = useState(false);
  const [audioDataListener, setAudioDataListener] = useState<any>(null);
  const [isPaused, setIsPaused] = useState(false);
  const [currentResponse, setCurrentResponse] = useState("");
  const [translatedResponse, setTranslatedResponse] = useState("");
  const [botIsSpeaking, setBotIsSpeaking] = useState<boolean>(false);

  // Initialize the Free Talk session when component mounts
  useEffect(() => {
    const initializeChat = async () => {
      if (!chatState.chatId && !chatState.requested) {
        chatState.setRequested(true);
        await startTopic(
          chatState.newChat || false,
          topicItem,
          chatState.setChatId,
          chatState.setTranscriptionResult,
          chatState.setRequested,
          chatState.setScores,
          chatState.dispatch,
          initNewChat,
          chatState.keySize,
          level || "beginner",
          chatState.approvedNewChat,
          isPremium,
          coins
        );
      }
    };
    initializeChat();
  }, []);

  // Hide tab bar when this screen is focused
  const isFocused = useIsFocused();
  useEffect(() => {
    if (isFocused) {
      navigation.getParent()?.setOptions({ tabBarStyle: { display: "none" } });
    }
    return () => {
      navigation.getParent()?.setOptions({ tabBarStyle: { display: "flex" } });
    };
  }, [navigation, isFocused]);

  // Start recording when the assistant finishes speaking
  useEffect(() => {
    if (!botIsSpeaking && currentResponse !== "" && !isPaused) {
      startRecordingSession();
    }
  }, [botIsSpeaking, currentResponse, isPaused]);

  // Cleanup speech when screen loses focus
  useEffect(() => {
    if (!isFocused) {
      Speech.stop();
      setBotIsSpeaking(false);
    }
  }, [isFocused]);

  // Initialize recording
  useEffect(() => {
    initRecording();
    return () => {
      if (audioDataListener) {
        audioDataListener.remove();
      }
    };
  }, []);

  // Handle recording completion
  const recordingComplete = async (uri: string) => {
    setIsRecording(false);
    if (uri && chatState.chatId) {
      await handleRecordingComplete(
        chatState.transcriptionResult,
        chatState.setTranscriptionResult as any,
        chatState.chatId,
        uri,
        chatState.setTextSubmitted
      );
    }
  };

  // Handle chat request after transcription
  useEffect(() => {
    const handleChatRequest = async () => {
      if (chatState.textSubmitted && chatState.transcriptionResult.length > 0) {
        const lastItem = chatState.transcriptionResult[chatState.transcriptionResult.length - 1];
        if (lastItem.type === "loading") {
          try {
            await chatRequest(
              lastItem.key,
              chatState.transcriptionResult,
              chatState.chatId,
              topicItem,
              chatState.setTranscriptionResult as any,
              chatState.setPlaySpeech,
              chatState.setHasReachedDailyLimit,
              () => {},
              level || "beginner",
              navigation,
              chatState.setChatFinished
            );
          } catch (error) {
            console.error("Chat request error:", error);
          }
        }
        chatState.setTextSubmitted(false);
      }
    };
    handleChatRequest();
  }, [chatState.textSubmitted, chatState.transcriptionResult, chatState.chatId]);

  // Handle speech synthesis when playSpeech is true
  useEffect(() => {
    if (chatState.playSpeech && chatState.transcriptionResult.length > 0) {
      const lastResponse = chatState.transcriptionResult[chatState.transcriptionResult.length - 1];
      if (lastResponse.type === "res" && lastResponse.text) {
        setCurrentResponse(lastResponse.text);
        getTranslation(lastResponse.text).then((translation) => {
          setTranslatedResponse(translation);
        }).catch((error) => {
          console.error("Translation error:", error);
          setTranslatedResponse("");
        });
        setBotIsSpeaking(true);
        Speech.speak(lastResponse.text, {
          onDone: onSpeechEnd,
          onStopped: () => setBotIsSpeaking(false),
          language: "en-US",
          rate: 0.9,
        });
        chatState.setPlaySpeech(false);
      }
    }
  }, [chatState.playSpeech, chatState.transcriptionResult]);

  // Start recording session
  const startRecordingSession = async () => {
    if (!isRecording && !isPaused) {
      setIsRecording(true);
      try {
        const listener = startRecording(
          () => {
            handleStopRecording();
          },
          () => {
            console.log("Speech detected");
          }
        );
        setAudioDataListener(listener);
      } catch (error) {
        console.error("Error starting recording:", error);
        setIsRecording(false);
      }
    }
  };

  // Stop recording
  const handleStopRecording = async () => {
    if (isRecording && audioDataListener) {
      try {
        audioDataListener.remove();
        setAudioDataListener(null);
        const uri = await stopRecording();
        if (uri) {
          await recordingComplete(uri);
        }
      } catch (error) {
        console.error("Error stopping recording:", error);
        setIsRecording(false);
      }
    }
  };

  // Handle end call
  const handleEndCall = () => {
    Speech.stop();
    setBotIsSpeaking(false);
    if (audioDataListener) {
      audioDataListener.remove();
    }
    navigation.goBack();
  };

  // Function called when speech ends
  const onSpeechEnd = () => {
    setBotIsSpeaking(false);
    if (!isPaused) {
      setTimeout(() => {
        startRecordingSession();
      }, 500);
    }
  };

  // Handle pause/resume
  const handlePauseResume = () => {
    const newPausedState = !isPaused;
    setIsPaused(newPausedState);
    if (newPausedState) {
      Speech.stop();
      setBotIsSpeaking(false);
      if (isRecording) {
        handleStopRecording();
      }
    } else {
      if (currentResponse) {
        setBotIsSpeaking(true);
        Speech.speak(currentResponse, {
          onDone: onSpeechEnd,
          onStopped: () => setBotIsSpeaking(false),
        });
      }
    }
  };

  return {
    isRecording,
    isPaused,
    botIsSpeaking,
    currentResponse,
    translatedResponse,
    startRecordingSession,
    handleStopRecording,
    handleEndCall,
    handlePauseResume,
    setBotIsSpeaking,
    setIsPaused,
    setCurrentResponse,
    setTranslatedResponse,
    recordingComplete,
    Speech,
  };
}
